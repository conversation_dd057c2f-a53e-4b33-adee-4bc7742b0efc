"use client"

import { useState, useRef } from "react"
import { toast } from "sonner"
import { Download, Upload, CheckCircle, XCircle, AlertCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import stockService from "@/lib/api/stockService"
import { validateStockCSV, ValidationResult } from "@/lib/utils/csvValidator"

interface ImportStockDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function StockImportExportDialog({ open, onOpenChange, onSuccess }: ImportStockDialogProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [errors, setErrors] = useState<Array<{ row: number; message: string }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Check if it's a CSV file
      if (!selectedFile.name.endsWith('.csv')) {
        toast.error("Please select a CSV file")
        return
      }

      setFile(selectedFile)
      setErrors([])
      setValidationResult(null)

      // Validate the CSV file immediately
      setIsValidating(true)
      try {
        const result = await validateStockCSV(selectedFile)
        setValidationResult(result)

        if (!result.isValid) {
          setErrors(result.errors.map(err => ({ row: err.row, message: err.message })))
          toast.error(`Validation failed: ${result.errors.length} errors found`)
        } else {
          toast.success(`File validated successfully: ${result.validRowCount} valid rows`)
        }
      } catch (error: any) {
        toast.error(`Validation failed: ${error.message}`)
        setValidationResult({ isValid: false, errors: [{ row: 0, message: error.message }], rowCount: 0, validRowCount: 0 })
      } finally {
        setIsValidating(false)
      }
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'
      const url = `${baseUrl}/stock/template`

      // Get the auth token
      const token = localStorage.getItem('auth_token')

      // Create headers with authorization token
      const headers: HeadersInit = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      toast.info("Downloading CSV template...")

      // Make the fetch request
      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        throw new Error('Failed to download template')
      }

      // Get the CSV content
      const csvContent = await response.text()

      // Create a download link
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = 'stock-import-template.csv'
      document.body.appendChild(a)
      a.click()

      // Clean up
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)

      toast.success("Template downloaded successfully")
    } catch (error) {
      console.error("Error downloading template:", error)
      toast.error("Failed to download template. Please try again.")
    }
  }

  const handleExportStock = async () => {
    try {
      toast.info("Exporting stock data...");

      // Use the API client's endpoint directly
      const response = await stockService.exportStockItems();

      // Create a download link for the blob
      const blob = new Blob([response], { type: 'text/csv' });
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = 'stock-export.csv';
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

      toast.success("Stock data exported successfully");
    } catch (error) {
      console.error("Error exporting stock:", error);
      toast.error("Failed to export stock data. Please try again.");
    }
  }

  const handleImport = async () => {
    if (!file) {
      toast.error("Please select a CSV file to import")
      return
    }

    if (!validationResult?.isValid) {
      toast.error("Please fix validation errors before importing")
      return
    }

    setIsUploading(true)
    setErrors([])

    console.log("Starting import with file:", file.name, file.type, file.size)

    try {
      toast.info("Importing stock data...");

      // Use the stock service to import the file
      const result = await stockService.importStockItems(file);

      console.log("Import result:", result);

      if (result.success) {
        toast.success(`Successfully imported ${result.imported} stock items`);
        onOpenChange(false);
        onSuccess();
        setFile(null);
        setValidationResult(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else {
        // Handle partial success or failure
        if (result.imported > 0) {
          toast.warning(`Partially imported: ${result.imported} successful, ${result.failed} failed`);
        } else {
          toast.error(`Import failed: ${result.failed} errors`);
        }

        if (result.errors && result.errors.length > 0) {
          setErrors(result.errors);
        }
      }
    } catch (error: any) {
      console.error("Error importing stock:", error)
      const errorMsg = error?.message || 'Import failed';
      toast.error(`Import failed: ${errorMsg}`);
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[70vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Stock Import/Export</DialogTitle>
          <DialogDescription>
            Download a CSV template, upload a CSV file to import stock items, or export current stock data to CSV.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDownloadTemplate}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Template
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportStock}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Stock
            </Button>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="csv-file">CSV File</Label>
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              ref={fileInputRef}
              onChange={handleFileChange}
              disabled={isValidating}
            />
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                {file ? `Selected file: ${file.name}` : "No file selected"}
              </p>
              {isValidating && (
                <Badge variant="secondary">Validating...</Badge>
              )}
              {validationResult && !isValidating && (
                <Badge variant={validationResult.isValid ? "default" : "destructive"}>
                  {validationResult.isValid ? (
                    <>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Valid ({validationResult.validRowCount} rows)
                    </>
                  ) : (
                    <>
                      <XCircle className="w-3 h-3 mr-1" />
                      {validationResult.errors.length} errors
                    </>
                  )}
                </Badge>
              )}
            </div>
          </div>

          {errors.length > 0 && (
            <div className="mt-4 rounded-md bg-destructive/10 p-3 max-h-48 overflow-y-auto">
              <div className="flex items-center mb-2">
                <AlertCircle className="w-4 h-4 mr-2 text-destructive" />
                <h4 className="font-medium text-destructive">
                  {validationResult ? 'Validation Errors:' : 'Import Errors:'}
                </h4>
              </div>
              <ul className="list-inside list-disc text-sm text-destructive space-y-1">
                {errors.slice(0, 20).map((error, index) => (
                  <li key={index}>
                    {error.row > 0 ? `Row ${error.row}: ` : ''}{error.message}
                  </li>
                ))}
                {errors.length > 20 && (
                  <li className="text-muted-foreground">
                    ... and {errors.length - 20} more errors
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleImport}
            disabled={!file || isUploading || isValidating || !validationResult?.isValid}
          >
            {isUploading ? (
              "Importing..."
            ) : isValidating ? (
              "Validating..."
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
