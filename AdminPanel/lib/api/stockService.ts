import apiClient from './apiClient'

export interface StockItem {
  id: string
  type: string
  gsm: number
  bf: number
  rollsAvailable: number
  width: number
  immediatePrice: number
  thirtyDayPrice: number
  sixtyDayPrice: number
  createdAt: string
  updatedAt: string
}

export interface CreateStockItemDto {
  type: string
  gsm: number
  bf: number
  rollsAvailable: number
  width: number
  immediatePrice: number
  thirtyDayPrice: number
  sixtyDayPrice: number
}

export interface UpdateStockItemDto {
  rollsAvailable?: number
  width?: number
  immediatePrice?: number
  thirtyDayPrice?: number
  sixtyDayPrice?: number
}

export interface StockQueryParams {
  page?: number
  limit?: number
  search?: string
  type?: string
  gsm?: number
  bf?: number
  width?: number
  minPrice?: number
  maxPrice?: number
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

interface StockResponse {
  items: StockItem[]
  pagination: {
    totalItems: number
    totalPages: number
    currentPage: number
    itemsPerPage: number
  }
}

const stockService = {
  getAllStockItems: async (params: StockQueryParams = {}): Promise<StockResponse> => {
    try {
      const response = await apiClient.get<any>('/stock/admin', params);
      const data = response.data;

      // Map backend response to frontend format
      return {
        items: data.stocks || [],
        pagination: {
          totalItems: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 1,
          currentPage: data.pagination?.page || 1,
          itemsPerPage: data.pagination?.limit || 10,
        },
      };
    } catch (error) {
      console.error("Error fetching stock items:", error)
      throw error
    }
  },

  getStockItemById: async (id: string): Promise<StockItem> => {
    try {
      const response = await apiClient.get<any>(`/stock/${id}/admin`);
      return response.data.stock;
    } catch (error) {
      console.error(`Error fetching stock item with ID ${id}:`, error)
      throw error
    }
  },

  createStockItem: async (stockItem: CreateStockItemDto): Promise<StockItem> => {
    try {
      const response = await apiClient.post<any>('/stock', stockItem);
      return response.data.stock;
    } catch (error) {
      console.error("Error creating stock item:", error)
      throw error
    }
  },

  updateStockItem: async (id: string, stockItem: UpdateStockItemDto): Promise<StockItem> => {
    try {
      const response = await apiClient.put<any>(`/stock/${id}`, stockItem);
      return response.data.stock;
    } catch (error) {
      console.error(`Error updating stock item with ID ${id}:`, error)
      throw error
    }
  },

  deleteStockItem: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/stock/${id}`);
    } catch (error) {
      console.error(`Error deleting stock item with ID ${id}:`, error)
      throw error
    }
  },

  importStockItems: async (file: File): Promise<{ success: boolean; imported: number; failed: number; errors?: Array<{ row: number; message: string }> }> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiClient.post<any>('/stock/import', formData, 'multipart/form-data');

      // Handle both success and error responses from backend
      if (response.data) {
        return {
          success: response.data.success || false,
          imported: response.data.imported || 0,
          failed: response.data.failed || 0,
          errors: response.data.errors || []
        };
      }

      // Fallback for unexpected response structure
      return {
        success: false,
        imported: 0,
        failed: 0,
        errors: [{ row: 0, message: 'Unexpected response format' }]
      };
    } catch (error: any) {
      console.error("Error importing stock items:", error);

      // Try to extract error details from the response
      if (error.errors && Array.isArray(error.errors)) {
        return {
          success: false,
          imported: 0,
          failed: error.errors.length,
          errors: error.errors
        };
      }

      throw error;
    }
  },

  exportStockItems: async (): Promise<Blob> => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/stock/export/admin`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export stock items');
      }

      return await response.blob();
    } catch (error) {
      console.error("Error exporting stock items:", error)
      throw error
    }
  },
}

export default stockService
