/**
 * CSV Validation Utility for Stock Import
 */

export interface ValidationError {
  row: number;
  column?: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  rowCount: number;
  validRowCount: number;
}

// Required CSV headers for stock import
const REQUIRED_HEADERS = [
  'type',
  'gsm',
  'bf',
  'width',
  'rollsAvailable',
  'immediatePrice',
  'thirtyDayPrice',
  'sixtyDayPrice'
];

// Valid values for specific fields
const VALID_GSM = [80, 100, 120, 140, 180, 200, 230, 250, 280, 300, 320, 330, 350];
const VALID_BF = [16, 18, 20, 22, 25, 28, 30, 35];

/**
 * Parse CSV content and return rows as objects
 */
function parseCSV(csvContent: string): any[] {
  const lines = csvContent.trim().split('\n');
  if (lines.length < 2) {
    throw new Error('CSV must contain at least a header row and one data row');
  }

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const rows: any[] = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
    const row: any = { rowIndex: i + 1 };
    
    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });
    
    rows.push(row);
  }

  return rows;
}

/**
 * Validate a single stock data row
 */
function validateStockRow(row: any, rowIndex: number): ValidationError[] {
  const errors: ValidationError[] = [];

  // Check required fields
  if (!row.type || row.type.trim() === '') {
    errors.push({
      row: rowIndex,
      column: 'type',
      message: 'Type is required'
    });
  }

  // Validate GSM
  const gsm = parseInt(row.gsm, 10);
  if (isNaN(gsm) || gsm <= 0) {
    errors.push({
      row: rowIndex,
      column: 'gsm',
      message: 'GSM must be a positive number'
    });
  } else if (!VALID_GSM.includes(gsm)) {
    errors.push({
      row: rowIndex,
      column: 'gsm',
      message: `GSM must be one of: ${VALID_GSM.join(', ')}`
    });
  }

  // Validate BF (Bursting Factor)
  const bf = parseInt(row.bf, 10);
  if (isNaN(bf) || bf <= 0) {
    errors.push({
      row: rowIndex,
      column: 'bf',
      message: 'BF must be a positive number'
    });
  } else if (!VALID_BF.includes(bf)) {
    errors.push({
      row: rowIndex,
      column: 'bf',
      message: `BF must be one of: ${VALID_BF.join(', ')}`
    });
  }

  // Validate width
  const width = parseInt(row.width, 10);
  if (isNaN(width) || width <= 0) {
    errors.push({
      row: rowIndex,
      column: 'width',
      message: 'Width must be a positive number'
    });
  }

  // Validate rollsAvailable
  const rollsAvailable = parseInt(row.rollsAvailable, 10);
  if (isNaN(rollsAvailable) || rollsAvailable < 0) {
    errors.push({
      row: rowIndex,
      column: 'rollsAvailable',
      message: 'Rolls available must be a non-negative number'
    });
  }

  // Validate prices
  const immediatePrice = parseFloat(row.immediatePrice);
  if (isNaN(immediatePrice) || immediatePrice <= 0) {
    errors.push({
      row: rowIndex,
      column: 'immediatePrice',
      message: 'Immediate price must be a positive number'
    });
  }

  const thirtyDayPrice = parseFloat(row.thirtyDayPrice);
  if (isNaN(thirtyDayPrice) || thirtyDayPrice <= 0) {
    errors.push({
      row: rowIndex,
      column: 'thirtyDayPrice',
      message: '30-day price must be a positive number'
    });
  }

  const sixtyDayPrice = parseFloat(row.sixtyDayPrice);
  if (isNaN(sixtyDayPrice) || sixtyDayPrice <= 0) {
    errors.push({
      row: rowIndex,
      column: 'sixtyDayPrice',
      message: '60-day price must be a positive number'
    });
  }

  return errors;
}

/**
 * Validate CSV file for stock import
 */
export async function validateStockCSV(file: File): Promise<ValidationResult> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const csvContent = e.target?.result as string;
        const errors: ValidationError[] = [];
        
        // Parse CSV
        let rows: any[];
        try {
          rows = parseCSV(csvContent);
        } catch (parseError: any) {
          return resolve({
            isValid: false,
            errors: [{ row: 0, message: parseError.message }],
            rowCount: 0,
            validRowCount: 0
          });
        }

        // Check headers
        const csvLines = csvContent.trim().split('\n');
        const headers = csvLines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        const missingHeaders = REQUIRED_HEADERS.filter(header => !headers.includes(header));
        if (missingHeaders.length > 0) {
          errors.push({
            row: 1,
            message: `Missing required headers: ${missingHeaders.join(', ')}`
          });
        }

        // Validate each row
        let validRowCount = 0;
        rows.forEach((row, index) => {
          const rowErrors = validateStockRow(row, row.rowIndex);
          if (rowErrors.length === 0) {
            validRowCount++;
          }
          errors.push(...rowErrors);
        });

        resolve({
          isValid: errors.length === 0,
          errors,
          rowCount: rows.length,
          validRowCount
        });
      } catch (error: any) {
        reject(new Error(`Failed to validate CSV: ${error.message}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsText(file);
  });
}
