'use client';

import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image } from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import StatusBarManager from '../../components/common/StatusBarManager';
import { useAuth } from '../../store/auth/AuthContext';
import { isValidEmail, isValidPhoneNumber } from '../../utils/formValidation';
import errorHandler from '../../utils/errorHandler';
import { sendPhoneVerificationCode } from '../../utils/firebase';

/**
 * Login screen for the app
 */
const LoginScreen = ({ navigation }) => {
  const { login } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loginMethod, setLoginMethod] = useState('email'); // 'email' or 'phone'

  // Handle email/password login
  const handleEmailLogin = async () => {
    // Validate email
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    // Validate password
    if (!password) {
      Alert.alert('Error', 'Please enter your password');
      return;
    }

    setIsLoading(true);
    try {
      const response = await login(email, password);
      console.log('Login response:', response.data.rider);

      // Check if login was successful
      if (response.status === 'success') {
        // Check if user is approved
      //   if (response.data && response.data.rider && response.data.rider.isApproved) {
      //     // User is approved, navigate to main app
      //     navigation.reset({
      //       index: 0,
      //       routes: [{ name: 'Main' }],
      //     });
      //   } else {
      //     // User is not approved, navigate to approval pending screen
      //     navigation.reset({
      //       index: 0,
      //       routes: [{ name: 'ApprovalPending' }],
      //     });
      //   }
      }
    } catch (error) {
      console.error('Login error:', error);
      errorHandler.handleApiError(error, {
        context: 'login',
        fallbackMessage: 'Please check your credentials and try again',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle phone OTP request
  const handlePhoneOtpRequest = async () => {
    if (!phoneNumber) {
      Alert.alert('Error', 'Please enter your phone number');
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    try {
      // Use the Firebase function directly instead of going through the API
      const formattedPhone = phoneNumber;
      console.log('Requesting OTP for phone number:', formattedPhone);
      
      const response = await sendPhoneVerificationCode(formattedPhone);
      
      if (response.success) {
        navigation.navigate('OTPVerification', {
          verificationType: 'phone',
          phoneNumber: formattedPhone,
          verificationId: response.verificationId,
          isLogin: true,
          message: 'Please enter the verification code sent to your phone number to log in.',
        });
      } else {
        // Handle error from Firebase
        Alert.alert('Error', response.error || 'Failed to send OTP to your phone');
      }
    } catch (error) {
      console.error('Phone OTP request error:', error);
      errorHandler.handleApiError(error, {
        context: 'phone_otp_request',
        fallbackMessage: 'Failed to send OTP to your phone',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps="handled"
    >
      <StatusBarManager />

      <View style={styles.logoContainer}>
        <Image
          source={require('../../assets/images/rukmini_ventures_logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.welcomeText}>Welcome to</Text>
        <Text style={styles.companyText}>Rukmini Ventures</Text>
      </View>

      <View style={styles.loginContainer}>
        <Text style={styles.loginTitle}>Login</Text>

        {loginMethod === 'email' ? (
          <>
            <Input
              label="Email"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.inputMargin}
            />

            <Input
              label="Password"
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              secureTextEntry
              style={styles.inputMargin}
            />

            <Button title="Continue with Email" onPress={handleEmailLogin} loading={isLoading} style={styles.button} />

            <TouchableOpacity style={styles.methodSwitch} onPress={() => setLoginMethod('phone')}>
              <Text style={styles.methodSwitchText}>Continue with Phone Number</Text>
            </TouchableOpacity>
          </>
        ) : (
          <>
            <Input
              label="Phone Number"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
              style={styles.inputMargin}
            />

            <Button title="Next" onPress={handlePhoneOtpRequest} loading={isLoading} style={styles.button} />

            <TouchableOpacity style={styles.methodSwitch} onPress={() => setLoginMethod('email')}>
              <Text style={styles.methodSwitchText}>Continue with Email</Text>
            </TouchableOpacity>
          </>
        )}

        <View style={styles.footer}>
          <Text style={styles.footerText}>Don't Have an Account?</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={styles.registerText}>Register</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: spacing.large,
  },
  logoContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xlarge,
    paddingHorizontal: spacing.medium,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: spacing.medium,
  },
  welcomeText: {
    ...textStyles.heading3,
    color: colors.textDark,
  },
  companyText: {
    ...textStyles.heading2,
    color: colors.primary,
    fontWeight: 'bold',
  },
  loginContainer: {
    paddingHorizontal: spacing.large,
    paddingTop: spacing.large,
  },
  loginTitle: {
    ...textStyles.heading2,
    color: colors.textDark,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  inputMargin: {
    marginBottom: spacing.medium,
  },
  button: {
    marginTop: spacing.medium,
  },
  methodSwitch: {
    marginTop: spacing.medium,
    alignItems: 'center',
  },
  methodSwitchText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.xlarge,
  },
  footerText: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  registerText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: 'bold',
    marginLeft: spacing.small,
  },
});

export default LoginScreen;
