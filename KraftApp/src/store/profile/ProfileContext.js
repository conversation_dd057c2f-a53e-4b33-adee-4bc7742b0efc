import React, { createContext, useState, useContext, useCallback } from 'react';
import { authApi } from '../../api';
import logger from '../../utils/simpleLogger';
import errorHandler from '../../utils/errorHandler';

// Create the profile context
const ProfileContext = createContext();

/**
 * Profile Provider component to manage user profile data
 */
export const ProfileProvider = ({ children }) => {
  const [profileData, setProfileData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastFetched, setLastFetched] = useState(null);

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;

  /**
   * Check if profile data is stale and needs to be refetched
   */
  const isProfileStale = useCallback(() => {
    if (!profileData || !lastFetched) {
      return true;
    }
    return Date.now() - lastFetched > CACHE_DURATION;
  }, [profileData, lastFetched, CACHE_DURATION]);

  /**
   * Fetch profile data from API
   */
  const fetchProfile = useCallback(async (force = false) => {
    // Don't fetch if data is fresh and not forced
    if (!force && !isProfileStale()) {
      console.log('Profile data is fresh, skipping fetch');
      return profileData;
    }

    // Don't fetch if already loading
    if (isLoading) {
      console.log('Profile fetch already in progress, skipping');
      return profileData;
    }

    setIsLoading(true);
    try {
      console.log('Fetching profile data from API...');
      const response = await authApi.getCurrentUser();
      
      if (response) {
        const now = Date.now();
        setProfileData(response);
        setLastFetched(now);
        
        console.log('Profile data updated successfully');
        return response;
      } else {
        console.warn('No profile data returned from API');
        return null;
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      errorHandler.handleApiError(error, {
        context: 'fetch_profile',
        fallbackMessage: 'Failed to load profile data. Please try again.',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [profileData, isProfileStale, isLoading]);

  /**
   * Update profile data locally after successful API update
   */
  const updateProfileData = useCallback((newData) => {
    setProfileData(newData);
    setLastFetched(Date.now());
  }, []);

  /**
   * Clear profile data (useful for logout)
   */
  const clearProfile = useCallback(() => {
    setProfileData(null);
    setLastFetched(null);
  }, []);

  /**
   * Get profile data, fetching if necessary
   */
  const getProfile = useCallback(async (force = false) => {
    if (force || isProfileStale()) {
      return await fetchProfile(force);
    }
    return profileData;
  }, [fetchProfile, isProfileStale, profileData]);

  const value = {
    profileData,
    isLoading,
    lastFetched,
    isProfileStale: isProfileStale(),
    fetchProfile,
    getProfile,
    updateProfileData,
    clearProfile,
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

/**
 * Custom hook to use the profile context
 */
export const useProfile = () => {
  const context = useContext(ProfileContext);

  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }

  return context;
};

export default ProfileContext;
