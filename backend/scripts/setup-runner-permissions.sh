#!/bin/bash

# Setup script for GitHub Actions self-hosted runner permissions
# This script should be run once on the EC2 instance to configure proper permissions

echo "🔧 Setting up GitHub Actions runner permissions..."

# Get the current user (should be the runner user)
RUNNER_USER=${USER}
echo "Setting up permissions for user: $RUNNER_USER"

# Create sudoers file for the runner user
SUDOERS_FILE="/etc/sudoers.d/github-actions-runner"

echo "Creating sudoers configuration..."
sudo tee $SUDOERS_FILE > /dev/null <<EOF
# GitHub Actions runner permissions
# Allow runner to clean workspace and manage file permissions
$RUNNER_USER ALL=(ALL) NOPASSWD: /bin/rm -rf *, /bin/chown -R $RUNNER_USER:$RUNNER_USER *, /bin/chmod -R * *
$RUNNER_USER ALL=(ALL) NOPASSWD: /usr/bin/rm -rf *, /usr/bin/chown -R $RUNNER_USER:$RUNNER_USER *, /usr/bin/chmod -R * *
EOF

# Set proper permissions on the sudoers file
sudo chmod 440 $SUDOERS_FILE

# Verify the sudoers file syntax
if sudo visudo -c -f $SUDOERS_FILE; then
    echo "✅ Sudoers configuration created successfully"
else
    echo "❌ Error in sudoers configuration"
    sudo rm -f $SUDOERS_FILE
    exit 1
fi

# Create the runner workspace directory if it doesn't exist
RUNNER_WORKSPACE="/home/<USER>/actions-runner/_work"
if [ ! -d "$RUNNER_WORKSPACE" ]; then
    echo "Creating runner workspace directory..."
    mkdir -p "$RUNNER_WORKSPACE"
fi

# Set proper ownership
sudo chown -R $RUNNER_USER:$RUNNER_USER "$RUNNER_WORKSPACE"

echo "🎉 GitHub Actions runner permissions setup complete!"
echo ""
echo "The runner user ($RUNNER_USER) now has the necessary permissions to:"
echo "- Clean workspace directories"
echo "- Fix file ownership and permissions"
echo "- Manage generated files from Prisma and other tools"
echo ""
echo "You can now run your GitHub Actions workflows without permission issues."
